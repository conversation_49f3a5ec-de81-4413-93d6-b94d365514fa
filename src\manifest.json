{"manifest_version": 3, "name": "__MSG_ext_title__", "short_name": "__MSG_ext_short_name__", "version": "********", "description": "__MSG_ext_description__", "author": "furyu", "default_locale": "en", "icons": {"16": "img/icon_16.png", "48": "img/icon_48.png", "96": "img/icon_96.png", "128": "img/icon_128.png"}, "content_scripts": [{"matches": ["*://x.com/*", "*://pbs.twimg.com/media/*", "*://tweetdeck.x.com/*", "*://mobile.x.com/*"], "js": ["js/jszip.min.js", "js/FileSaver.min.js", "js/inject_script.js", "js/tweet_api.js", "js/init.js"], "run_at": "document_start", "all_frames": true}, {"matches": ["*://x.com/*", "*://pbs.twimg.com/media/*", "*://tweetdeck.x.com/*", "*://mobile.x.com/*"], "js": ["js/twOpenOriginalImage.user.js"], "run_at": "document_end", "all_frames": true}], "permissions": ["contextMenus", "downloads", "storage"], "background": {"service_worker": "background-wrapper.js"}, "options_ui": {"page": "html/options.html", "open_in_tab": false}, "action": {"default_icon": "img/icon_48.png", "default_title": "__MSG_ext_title__", "default_popup": "html/options.html"}, "host_permissions": ["*://*.twimg.com/*", "*://*.x.com/*"], "web_accessible_resources": [{"matches": ["*://*.x.com/*"], "resources": ["js/*.js"]}]}