// ==UserScript==
// @name            Twitter/X Media Downloader
// @name:ja         Twitter/X メディアダウンローダー
// @namespace       http://tampermonkey.net/
// @version         1.0.0
// @description     Add download button to Twitter/X posts for images and videos
// @description:ja  Twitter/Xの投稿に画像・動画用のダウンロードボタンを追加
// <AUTHOR>
// @match           https://twitter.com/*
// @match           https://x.com/*
// @match           https://mobile.twitter.com/*
// @match           https://mobile.x.com/*
// @grant           GM_download
// @grant           GM_xmlhttpRequest
// @connect         twimg.com
// @connect         twitter.com
// @connect         x.com
// ==/UserScript==

(function() {
    'use strict';

    const BUTTON_STYLE = `
        .twitter-media-downloader-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 4px 12px;
            margin: 0 4px;
            background-color: rgba(15, 20, 25, 0.75);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 9999px;
            font-size: 13px;
            font-weight: 700;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .twitter-media-downloader-btn:hover {
            background-color: rgba(39, 44, 48, 0.75);
        }
        .twitter-media-downloader-btn svg {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }
    `;

    // Add styles to page
    const style = document.createElement('style');
    style.textContent = BUTTON_STYLE;
    document.head.appendChild(style);

    // Download icon SVG
    const DOWNLOAD_ICON = `<svg viewBox="0 0 24 24" fill="currentColor"><g><path d="M12 2.59l5.7 5.7-1.41 1.42L13 6.41V16h-2V6.41l-3.3 3.3-1.41-1.42L12 2.59zM21 15l-.02 3.51c0 1.38-1.12 2.49-2.5 2.49H5.5C4.11 21 3 19.88 3 18.5V15h2v3.5c0 .28.22.5.5.5h12.98c.28 0 .5-.22.5-.5L19 15h2z"></path></g></svg>`;

    // Extract tweet ID from URL or closest article element
    function getTweetId(element) {
        const article = element.closest('article');
        if (!article) return null;
        
        const link = article.querySelector('a[href*="/status/"]');
        if (!link) return null;
        
        const match = link.href.match(/\/status\/(\d+)/);
        return match ? match[1] : null;
    }

    // Get highest quality image URL
    function getOriginalImageUrl(url) {
        if (!url || !url.includes('pbs.twimg.com')) return url;
        
        // Remove any existing size parameters and add ?format=jpg&name=orig
        const baseUrl = url.split('?')[0].split('&')[0];
        const format = baseUrl.includes('.png') ? 'png' : 'jpg';
        return `${baseUrl}?format=${format}&name=orig`;
    }

    // Extract video URL from tweet (requires API call or DOM parsing)
    async function getVideoUrl(tweetElement) {
        // Try to find video element directly
        const video = tweetElement.querySelector('video');
        if (video && video.src) {
            return video.src;
        }

        // Look for video in blob URLs or source elements
        const sources = tweetElement.querySelectorAll('video source');
        for (const source of sources) {
            if (source.src && source.src.includes('.mp4')) {
                return source.src;
            }
        }

        return null;
    }

    // Download file using GM_download
    function downloadFile(url, filename) {
        if (!url) return;

        GM_download({
            url: url,
            name: filename,
            saveAs: false,
            onerror: function(err) {
                console.error('Download failed:', err);
                // Fallback to window.open
                window.open(url, '_blank');
            },
            onload: function() {
                console.log('Download completed:', filename);
            }
        });
    }

    // Create download button
    function createDownloadButton() {
        const button = document.createElement('div');
        button.className = 'twitter-media-downloader-btn';
        button.innerHTML = DOWNLOAD_ICON + 'Download';
        return button;
    }

    // Add download button to tweet
    function addDownloadButton(article) {
        // Check if button already exists
        if (article.querySelector('.twitter-media-downloader-btn')) return;

        // Find action bar (where like, retweet buttons are)
        const actionBar = article.querySelector('[role="group"]');
        if (!actionBar) return;

        // Check if tweet has media
        const hasImage = article.querySelector('img[src*="pbs.twimg.com"]');
        const hasVideo = article.querySelector('video, [data-testid="videoPlayer"]');
        
        if (!hasImage && !hasVideo) return;

        const button = createDownloadButton();
        
        button.addEventListener('click', async (e) => {
            e.preventDefault();
            e.stopPropagation();

            const tweetId = getTweetId(article);
            const timestamp = new Date().getTime();

            if (hasVideo) {
                const videoUrl = await getVideoUrl(article);
                if (videoUrl) {
                    const filename = `twitter_${tweetId}_${timestamp}.mp4`;
                    downloadFile(videoUrl, filename);
                } else {
                    alert('Could not find video URL. The video might be protected.');
                }
            } else if (hasImage) {
                const images = article.querySelectorAll('img[src*="pbs.twimg.com"]');
                
                if (images.length === 1) {
                    // Single image
                    const imageUrl = getOriginalImageUrl(images[0].src);
                    const ext = imageUrl.includes('format=png') ? 'png' : 'jpg';
                    const filename = `twitter_${tweetId}_${timestamp}.${ext}`;
                    downloadFile(imageUrl, filename);
                } else if (images.length > 1) {
                    // Multiple images
                    images.forEach((img, index) => {
                        const imageUrl = getOriginalImageUrl(img.src);
                        const ext = imageUrl.includes('format=png') ? 'png' : 'jpg';
                        const filename = `twitter_${tweetId}_${timestamp}_${index + 1}.${ext}`;
                        downloadFile(imageUrl, filename);
                    });
                }
            }
        });

        // Insert button into action bar
        const lastButton = actionBar.lastElementChild;
        if (lastButton) {
            actionBar.insertBefore(button, lastButton);
        } else {
            actionBar.appendChild(button);
        }
    }

    // Observer to detect new tweets
    const observer = new MutationObserver((mutations) => {
        const articles = document.querySelectorAll('article[data-testid="tweet"]');
        articles.forEach(article => {
            addDownloadButton(article);
        });
    });

    // Start observing
    function init() {
        // Process existing tweets
        const articles = document.querySelectorAll('article[data-testid="tweet"]');
        articles.forEach(article => {
            addDownloadButton(article);
        });

        // Observe for new tweets
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // Wait for page to load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();