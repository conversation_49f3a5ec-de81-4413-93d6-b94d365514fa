{"manifest_version": 2, "name": "__MSG_ext_title__", "short_name": "__MSG_ext_short_name__", "version": "********", "description": "__MSG_ext_description__", "author": "furyu", "default_locale": "en", "icons": {"16": "img/icon_16.png", "48": "img/icon_48.png", "96": "img/icon_96.png", "128": "img/icon_128.png"}, "content_scripts": [{"matches": ["*://x.com/*", "*://pbs.twimg.com/media/*", "*://tweetdeck.x.com/*", "*://mobile.x.com/*"], "js": ["js/jszip.min.js", "js/FileSaver.min.js", "js/inject_script.js", "js/tweet_api.js", "js/init.js"], "run_at": "document_start", "all_frames": true}, {"matches": ["*://x.com/*", "*://pbs.twimg.com/media/*", "*://tweetdeck.x.com/*", "*://mobile.x.com/*"], "js": ["js/twOpenOriginalImage.user.js"], "run_at": "document_end", "all_frames": true}], "permissions": ["contextMenus", "downloads", "storage", "*://*.twimg.com/*", "*://*.x.com/*"], "background": {"scripts": ["js/background.js"], "persistent": false}, "options_ui": {"page": "html/options.html"}, "browser_action": {"default_icon": "img/icon_48.png", "default_title": "__MSG_ext_title__", "default_popup": "html/options.html"}, "web_accessible_resources": ["js/*.js"]}